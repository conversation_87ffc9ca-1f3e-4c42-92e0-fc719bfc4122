{"name": "taskmapper-frontend", "type": "module", "version": "3.1.8", "private": true, "homepage": "https://github.com/sensehawk/taskmapper-frontend", "repository": {"type": "git", "url": "https://github.com/sensehawk/taskmapper-frontend.git"}, "engines": {"npm": ">=10.8.0", "pnpm": ">=8.15.0", "node": ">=20.17.0"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "scripts": {"build": "vite build", "preview": "vite preview --port 8080", "dev": "vite --port 8080 --open", "lint": "eslint .", "lintf": "eslint . --fix", "taze": "taze major -I", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@datadog/browser-rum": "^5.27.0", "@envis/vcolor-picker": "^1.5.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.11.1", "@faker-js/faker": "^9.0.2", "@headlessui/vue": "^1.7.23", "@mapbox/mapbox-gl-sync-move": "^0.3.1", "@mapbox/togeojson": "^0.16.2", "@noction/vue-draggable-grid": "^1.11.0", "@okta/okta-auth-js": "^7.8.0", "@sensehawk/chart-generator": "^0.1.7", "@sensehawk/expression-editor": "^0.1.1", "@sentry/vite-plugin": "^2.22.4", "@sentry/vue": "^8.31.0", "@splitsoftware/splitio": "^10.28.0", "@tanstack/vue-table": "^8.20.5", "@tiptap/extension-bold": "^2.7.2", "@tiptap/extension-bullet-list": "^2.7.2", "@tiptap/extension-color": "^2.7.2", "@tiptap/extension-document": "^2.7.2", "@tiptap/extension-dropcursor": "^2.7.2", "@tiptap/extension-gapcursor": "^2.7.2", "@tiptap/extension-hard-break": "^2.7.2", "@tiptap/extension-heading": "^2.7.2", "@tiptap/extension-highlight": "^2.7.2", "@tiptap/extension-history": "^2.7.2", "@tiptap/extension-horizontal-rule": "^2.7.2", "@tiptap/extension-image": "^2.7.2", "@tiptap/extension-italic": "^2.7.2", "@tiptap/extension-link": "^2.7.2", "@tiptap/extension-list-item": "^2.7.2", "@tiptap/extension-mention": "^2.7.2", "@tiptap/extension-ordered-list": "^2.7.2", "@tiptap/extension-paragraph": "^2.7.2", "@tiptap/extension-placeholder": "^2.7.2", "@tiptap/extension-strike": "^2.7.2", "@tiptap/extension-table": "^2.7.2", "@tiptap/extension-table-cell": "^2.7.2", "@tiptap/extension-table-header": "^2.7.2", "@tiptap/extension-table-row": "^2.7.2", "@tiptap/extension-text": "^2.7.2", "@tiptap/extension-text-align": "^2.7.2", "@tiptap/extension-text-style": "^2.7.2", "@tiptap/extension-underline": "^2.7.2", "@tiptap/pm": "^2.7.2", "@tiptap/suggestion": "^2.7.2", "@tiptap/vue-3": "^2.7.2", "@turf/turf": "^7.1.0", "@uppy/aws-s3": "^4.1.0", "@uppy/core": "^4.2.0", "@vueform/plugin-mask": "^1.0.7", "@vueform/vueform": "^1.10.10", "@vuepic/vue-datepicker": "^9.0.3", "@vueuse/components": "^11.1.0", "@vueuse/core": "^11.1.0", "@vueuse/integrations": "^11.1.0", "any-date-parser": "^2.0.0", "axios": "^1.7.7", "browser-image-resizer": "^2.4.1", "buffer": "^6.0.3", "click-outside-vue3": "^4.0.1", "comlink": "^4.4.1", "compressorjs": "^1.2.1", "convert": "^5.4.1", "currency-symbol-map": "^5.1.0", "dayjs": "^1.11.13", "dayjs-business-days2": "^1.2.2", "dhtmlx-gantt": "file:local_modules/dhtmlx-gantt", "dhtmlx-scheduler": "file:local_modules/dhtmlx-scheduler", "dompurify": "^3.1.6", "echarts": "^6.0.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "fusioncharts": "^4.1.2", "grid-layout-plus": "^1.1.0", "handsontable": "^15.1.0", "hot-formula-parser": "^4.0.0", "idb-keyval": "^6.2.1", "idle-tracker": "^0.1.3", "javascript-color-gradient": "^2.5.0", "js-md5": "^0.8.3", "json-rules-engine": "^6.5.0", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "mapbox-gl-draw-circle-mode": "file:local_modules/mapbox-circle-mode", "mapbox-gl-draw-rectangle-mode": "^1.0.4", "mapbox-gl-draw-snap-mode": "file:local_modules/mapbox-snap-mode", "marked": "^15.0.11", "mitt": "^3.0.1", "nanoid": "^5.0.7", "papaparse": "^5.4.1", "pdfmake": "^0.2.13", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^4.0.2", "prettysize": "^2.0.0", "prosemirror-state": "^1.4.3", "pusher-js": "8.4.0-rc2", "query-string": "^9.1.0", "quickchart-js": "^3.1.3", "rrule": "^2.8.1", "sanitize-s3-objectkey": "^0.0.1", "slugify": "^1.6.6", "splitpanes": "^3.1.5", "stream-chat": "^8.40.9", "tiptap-extension-image-freely": "file:local_modules/tiptap-extension-image-freely", "tiptap-extension-image-upload": "file:local_modules/tiptap-extension-image-upload", "ua-parser-js": "^1.0.39", "universal-cookie": "^7.2.0", "vue": "^3.5.8", "vue-final-modal": "^4.5.5", "vue-flexmonster": "^2.9.86", "vue-fusioncharts": "^3.3.0", "vue-router": "^4.4.5", "vue-signature-pad": "^3.0.2", "vue-tippy": "^6.4.4", "vue-toastification": "2.0.0-rc.5", "vue-virtual-scroller": "2.0.0-beta.8", "vue-virtual-tree": "file:local_modules/vue-virtual-tree", "vue3-emoji-picker": "^1.1.8", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^3.7.1", "@iconify/json": "^2.2.252", "@vitejs/plugin-vue": "^5.1.4", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "critters": "^0.0.24", "cz-git": "^1.9.4", "eslint": "^9.11.1", "eslint-plugin-command": "^0.2.6", "eslint-plugin-format": "^0.1.2", "lint-staged": "^15.2.10", "npm-force-resolutions": "^0.0.10", "postcss": "^8.4.47", "react": "^18.3.1", "react-dom": "^18.3.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.79.3", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.13", "taze": "^0.16.9", "typescript": "^5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.7", "vite-plugin-inspect": "^0.8.7", "vite-plugin-vue-inspector": "5.2.0", "vite-plugin-vue-layouts": "^0.11.0", "vitest": "^2.1.1", "vue-tsc": "^2.1.6"}, "resolutions": {"prosemirror-model": "1.19.2"}, "simple-git-hooks": {"pre-commit": "git rev-parse -q --no-revs --verify MERGE_HEAD || pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}