import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();

export const useBiStore = defineStore('bi', {
  state: () => ({
    all_tables: [],
    selected_table: null,
    selected_database: 'grs_corvera',
    stages: [],
    sql_query: '',
    data: [],
    selected_stage_index: null,
    are_chart_builder_fields_filled: false,
    chart_builder_config: {},
    chart_builder_data: [
      { Category: 'Design', Quarter: 'Q1', Planned: '10', Actual: 8, StartDate: '2021-01-01', isAllowed: true },
      { Category: 'Design', Quarter: 'Q1', Planned: '10', Actual: 10, StartDate: '2021-02-01', isAllowed: false },
      { Category: 'Design', Quarter: 'Q2', Planned: '25', Actual: 22, StartDate: '2021-03-01', isAllowed: true },
      { Category: 'Engineering', Quarter: 'Q1', Planned: '15', Actual: 14, StartDate: '2021-05-01', isAllowed: false },
      { Category: 'Engineering', Quarter: 'Q2', Planned: '18', Actual: 16, StartDate: '2021-06-01', isAllowed: true },
      { Category: 'Design and Engineering', Quarter: 'Q2', Planned: '18', Actual: 30, StartDate: '2025-08-28T06:32:10.189Z', isAllowed: true },
    ],
  }),
  getters: {
    chart_builder_data_types() {
      if (!this.chart_builder_data || this.chart_builder_data.length === 0) {
        return {};
      }

      const types = {};
      const sample_size = Math.max(1, Math.ceil(this.chart_builder_data.length * 0.05));

      const first_row = this.chart_builder_data[0];
      const column_names = Object.keys(first_row);

      const column_type_counts = {};
      column_names.forEach((column) => {
        column_type_counts[column] = {
          date: 0,
          numeric: 0,
          text: 0,
          boolean: 0,
          null_count: 0,
          total_checked: 0,
        };
      });

      for (let i = 0; i < sample_size && i < this.chart_builder_data.length; i++) {
        const row = this.chart_builder_data[i];

        for (const column of column_names) {
          const value = row[column];
          column_type_counts[column].total_checked++;

          if (value === null || value === undefined) {
            column_type_counts[column].null_count++;
            continue;
          }

          // Check if it's a pure number (not a date-like string)
          if (typeof value === 'number' || value instanceof Number) {
            column_type_counts[column].numeric++;
          }
          // For strings, check if it's purely numeric (not containing date separators)
          else if (typeof value === 'string' && /^\s*-?\d+(?:\.\d*)?\s*$/.test(value) && !Number.isNaN(Number.parseFloat(value))) {
            column_type_counts[column].numeric++;
          }
          // Check if value is a Date object or a date-like string
          else if (value instanceof Date || (typeof value === 'string' && !Number.isNaN(Date.parse(value)))) {
            column_type_counts[column].date++;
          }
          else if (typeof value === 'string') {
            column_type_counts[column].text++;
          }
          else if (typeof value === 'boolean') {
            column_type_counts[column].boolean++;
          }
        }
      }

      for (const column of column_names) {
        const counts = column_type_counts[column];
        const non_null_count = counts.total_checked - counts.null_count;

        if (non_null_count === 0) {
          types[column] = 'mixed';
          continue;
        }

        const type_percentages = {
          date: counts.date / non_null_count,
          numeric: counts.numeric / non_null_count,
          text: counts.text / non_null_count,
          boolean: counts.boolean / non_null_count,
        };

        const dominant_type = Object.keys(type_percentages).reduce((a, b) =>
          type_percentages[a] > type_percentages[b] ? a : b,
        );

        // If the dominant type represents 100% of non-null values, it is the type of that column
        if (type_percentages[dominant_type] === 1.0) {
          types[column] = dominant_type;
        }
        else {
          types[column] = 'mixed';
        }
      }

      return types;
    },
  },
  actions: {
    async getTables() {
      const response = await this.$services.bi_schema.table({
        id: this.selected_database,
      });
      this.all_tables = response.data.tables.map(table => ({ label: table.name, columns: table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ table_name: table.name, column_name: column.name }) })) }));
      await this.selectTable(this.all_tables[1]);
      return response.data;
    },
    async getTableColumns({ table_name }) {
      const response = await this.$services.bi_schema.columns({
        id: this.selected_database,
        table_name,
      });
      return response.data;
    },
    async selectTable(table) {
      this.selected_table = {
        label: table.label,
        columns: table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ column_name: column.name }) })),
      };
    },
    async getDataFromStages() {
      if (this.stages[0]?.value?.columns?.length === 0)
        return;
      const getField = (field, selected_table_name) => ({ field: field.table_name ? (`${field.table_name}→${field.label}`) : (`${selected_table_name}${field.label}`), agg: field.agg, alias: field.alias });
      const getExpression = field => ({ expr: field.expression, type: field.aggregation_type, alias: field.alias });
      const getJoin = (stage) => {
        if (stage.tables.length > 1) {
          const joins = stage.tables.filter((t, index) => index > 0).map((table) => {
            return {
              table: { name: table.label, alias: table.alias },
              type: table.type,
              on: { left: table.on.left, right: `${table.on.right}` },
            };
          });
          return joins;
        }
        else {
          return undefined;
        }
      };

      const getTableConfig = stage => ({ table: stage.selected_table?.label, orderBy: stage.value.orderBy.map(column => ({ column: column.alias, direction: column.direction })), limit: stage.value.limit ? stage.value.limit : undefined, columns: stage.value.columns.map(field => field.expression ? getExpression(field) : getField(field, stage.selected_table?.label ? `${stage.selected_table.label}→` : '')), joins: getJoin(stage) });
      const config = this.stages.filter(stage => stage.value?.columns?.length > 0).map(stage => getTableConfig(stage));
      const response = await this.$services.bi_query.execute({
        id: this.selected_database,
        body: config,
      });
      this.data = response.data?.data;
      this.sql_query = response.data?.sql;
      return response.data;
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
