<script setup>
import { ExpressionEditor } from '@sensehawk/expression-editor';
import { Validator } from '@vueform/vueform';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import { useCommonImports } from '~/common/composables/common-imports.composable';
import '@sensehawk/expression-editor/style.css';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  fields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['close', 'save']);
const { $t } = useCommonImports();
const form$ = ref(null);
const editor_ref = ref(null);
const type = ref('aggregation');
const all_columns = computed(() => props.tables.map(table => table.columns.map(column => ({ label: column.alias, type: column.type }))).flat());

const columns = computed(() => [
  ...all_columns.value,
  ...props.fields.map(field => ({
    label: field.alias || field.label,
    type: field.type,
  })),
]);

async function save() {
  await form$.value.validate();
  if (!form$.value.invalid && editor_ref.value.isValid) {
    emit('save', {
      label: form$.value.data.name,
      type: editor_ref.value.outputType,
      expression: editor_ref.value.expression,
      aggregation_type: type.value,
      alias: form$.value.data.name,
      fields: editor_ref.value.fields,
      same_stage: true,
      functions: editor_ref.value.functionsUsed,
    });
  }
}

const uniqueName = class extends Validator {
  get message() {
    return 'Name already exists';
  }

  check(value) {
    const list = columns.value.map(({ label }) => label);
    if (list.length > 1 && value) {
      const is_match = list.filter(name => name?.toLowerCase() === value?.trim()?.toLowerCase());
      if (is_match.length > 0)
        return false;
    }
    return true;
  }
};
</script>

<template>
  <div v-click-outside="() => emit('close')">
    <div class="fixed min-w-[480px] min-h-[300px] z-20 bg-white border rounded-lg shadow-lg flex flex-col left-72 top-60">
      <div class="w-full font-medium p-4 flex items-center justify-between border-b">
        <div class="flex items-center gap-2">
          {{ $t('Custom Expression') }}
        </div>
        <div class="flex items-center gap-2" @click="emit('close')">
          <hawk-button icon type="text" size="xs">
            <IconHawkXClose class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div class="flex-1 p-4">
        <vueform
          ref="form$"
          size="sm"
          :display-errors="false"
          :columns="{
            default: { container: 12, label: 12, wrapper: 12 },
            sm: { container: 12, label: 12, wrapper: 12 },
            md: { container: 12, label: 12, wrapper: 12 },
          }"
          @mounted="form$.validate()"
        >
          <TextElement name="name" :label="$t('Name')" :rules="['required', uniqueName]" />
        </vueform>
        <div class="mt-4 text-sm text-gray-700 font-medium">
          {{ $t('Type') }}
        </div>
        <div class="mt-1">
          <HawkButtonGroup
            :items="[
              { label: 'Aggregation', value: 'aggregation', leftSlot: IconHawkFunction },
              { label: 'Column', value: 'column', leftSlot: IconHawkFormula },
            ]"
            :active_item="type"
            active_key="value"
            @select="type = $event.value"
          />
        </div>
        <div class="mt-4 text-sm text-gray-700 font-medium">
          {{ $t('Expression') }}
        </div>
        <ExpressionEditor
          ref="editor_ref"
          class="mt-1"
          :columns="columns"
        />
        <div class="text-red-700 text-sm">
          {{ editor_ref?.errors?.[0]?.message }}
        </div>
      </div>
      <div class="flex w-full items-center justify-between p-4 border-t">
        <div />
        <div class="flex items-center gap-2">
          <hawk-button type="outlined" @click="emit('close')">
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button :disabled="form$?.invalid || !editor_ref?.isValid" @click="save">
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>
