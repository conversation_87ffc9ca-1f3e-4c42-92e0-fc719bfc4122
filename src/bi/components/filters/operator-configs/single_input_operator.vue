<script setup>
// --------------------------------- Imports -------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Props --------------------------------- //

// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //

// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// console.log('Single Input Operator Component Loaded', props);
</script>

<template>
  <div>
    <TextElement
      name="operator_value"
      :placeholder=" $t('Enter value')"
      class="pb-2 px-2"
      v-bind="componentConfig"
      autocomplete="off"
      rules="required"
      :default="filterConfig?.operator_value || null"
      @keydown.space.stop
    />
  </div>
</template>

<style scoped lang="scss">

</style>
