<script setup>
// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //
defineEmits(['search']);
// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //
// const checkbox_options = ref(props.columnConfig.options || []);
const search = ref('');
// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const checkbox_options = computed(() => {
  const options = props.columnConfig.options || [];
  return options.filter(option => option.toLowerCase().includes(search.value.trim().toLowerCase()));
});
const show_select_all = computed(() => {
  return checkbox_options.value.length > 1;
});
// -------------------------------- Functions ------------------------------- //
function toggleSelectAll(new_val, _old_val, el$) {
  if (new_val) {
    el$.form$.elements$.operator_value.checkAll();
  }
  else {
    el$.form$.elements$.operator_value.uncheckAll();
  }
}
// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// console.log('Checkbox Operator Component Loaded', props);
</script>

<template>
  <div>
    <HawkSearchInput
      v-model="search" :placeholder="$t('Search')" class="mb-4"
      full_width
      @update:model-value="$emit('search', $event)"
      @keydown.stop
    />
    <!-- <div class="flex">
      <HawkCheckbox :model-value="select_all" size="xs" @update:model-value="toggleSelectAll" />
      {{ $t('Select all') }}
    </div> -->
    <CheckboxElement v-if="show_select_all" name="select_all" :default="filterConfig?.select_all || false" @change="toggleSelectAll">
      {{ $t('Select all') }}
    </CheckboxElement>
    <CheckboxgroupElement rules="required" name="operator_value" :items="checkbox_options" :default="filterConfig?.operator_value || []" />
  </div>
</template>
