<script setup>
import BetweenOperator from '~/bi/components/filters/operator-configs/between_operator.vue';
import CheckboxOperator from '~/bi/components/filters/operator-configs/checkbox_operator.vue';
import Single_input_operator from '~/bi/components/filters/operator-configs/single_input_operator.vue';
import SliderOperator from '~/bi/components/filters/operator-configs/slider_operator.vue';

// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  selectedColumnConfig: {
    type: Object,
    required: true,
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  hasBack: {
    type: Boolean,
    default: true,
  },
});

// ---------------------------------- Emits --------------------------------- //
const emit = defineEmits(['apply', 'back', 'cancel']);
// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //
const component_map = {
  checkbox: CheckboxOperator,
  input: Single_input_operator,
  between: BetweenOperator,
  slider: SliderOperator,
  // 'text-element': TextInputComponent,
  // 'autocomplete-element': AutocompleteInputComponent,
  // 'radio-group-element': RadioGroupComponent,
  // 'radio-group-element-with-blank-not-blank': RadioGroupComponent,
  // 'number-element': TextInputComponent,
  // 'number-element-between': NumberRangeComponent,
  // 'multi-select-input': MultiSelectComponent,
  // 'single-value-select': MultiSelectComponent,
  // 'date-input': DateInputComponent
};

// ------------------------ Variables - Local - refs ------------------------ //
const form$ = ref(null);
const selected_operator = ref(props.filterConfig?.operator || props.selectedColumnConfig.default_operator || null);

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const current_component = computed(() => {
  return component_map[selected_operator.value?.component];
});
const component_props = computed(() => {
  const componentConfig = {
    // name: 'operator_value',
    // placeholder: 'Enter value',
    // defaultValue: props.selectedValue.value,
    // rules: 'required',
  };

  if ((selected_operator.value?.component === 'input' || props.selectedColumnConfig.default_operator.component === 'input') && ['number', 'float', 'integer'].includes(props.selectedColumnConfig.type)) {
    componentConfig.inputType = 'number';
  }
  else {
    componentConfig.inputType = 'text';
  }

  return { componentConfig, columnConfig: props.selectedColumnConfig, filterConfig: props.filterConfig };
  // switch (props.selectedColumnConfig.type) {
  //   case 'number':
  //     return {
  //       ...baseProps,
  //       option: props.selectedColumnConfig.options,
  //     };
  //   default:
  //     return baseProps;
  // }
});
// -------------------------------- Functions ------------------------------- //
async function onApply() {
  await form$.value.validate();
  console.log('Form data:', form$.value.invalid);
  if (form$.value.invalid)
    return;
  console.log('Apply clicked', form$.value.data, 'operator_type::', selected_operator.value);
  emit('apply', {
    column_config: props.selectedColumnConfig,
    filter_config: {
      operator: selected_operator.value,
      ...form$.value.data,
    },
  });
};
function onBack() {
  // console.log('Back clicked');
  emit('back');
};
function onCancel() {
  // console.log('Cancel clicked');
  emit('cancel');
};
// function getSelectedOperatorName() {
//   return selected_operator.value?.label;
// };
function onSelectOperator(operator, close = () => {}) {
  // console.log('Select operator clicked', operator);
  selected_operator.value = operator;
  close?.();
};
// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
console.log('Selected Column Config:', props.selectedColumnConfig, props.filterConfig);
</script>

<template>
  <div>
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <HawkButton v-if="hasBack" type="plain" size="xs" class="!px-2 !py-0" @click="onBack">
          <icon-hawk-arrow-left class="text-gray-600  " />
        </HawkButton>
        <div class="text-sm font-semibold text-gray-700 ml-2">
          {{ selectedColumnConfig.label }}
        </div>
      </div>
      <HawkMenu
        additional_trigger_classes="!ring-0 !focus:ring-0 !px-2 !py-0"
        :items="selectedColumnConfig.operators"
        position="fixed"
      >
        <template #trigger="{ open }">
          <div class="p-2 w-full flex items-center flex-1 gap-2 text-xs">
            {{ selected_operator.label }}
            <div>
              <IconHawkChevronUp v-if="open" />
              <IconHawkChevronDown v-else />
            </div>
          </div>
        </template>
        <template #content="{ close: closeOperatorMenu }">
          <div class="bg-white p-1 rounded-lg">
            <div
              v-for="operator in selectedColumnConfig.operators" :key="operator.value"
              class="px-3 h py-3 text-xs font-medium min-w-[10rem] rounded-lg cursor-pointer text-gray-700 hover:bg-gray-50 flex items-center"
              @click.stop="onSelectOperator(operator, closeOperatorMenu)"
            >
              {{ operator.label }}
              <IconHawkCheck v-if="selected_operator.value === operator.value" class="ml-auto text-primary-500 w-5 h-5 min-w-5" />
            </div>
          </div>
        </template>
      </HawkMenu>
    </div>
    <!-- Operator config -->
    <div class="mt-4 mb-2">
      <!-- {{ component_props }} -->
      <Vueform ref="form$" size="sm" class="px-2" :display-errors="false">
        <component
          :is="current_component"
          class="col-span-12"
          v-bind="component_props"
        >
        <!--
        @change="handleChange"
        @on-enter="handleEnter"
        @on-click="handleClick" -->
          <!-- <template v-if="$slots.item" #item="slotProps">
            <slot name="item" v-bind="slotProps" />
          </template> -->
        </component>
        <!-- Footer -->
        <div class="col-span-12 mt-2">
          <hr>
          <div class="mt-2 flex items-center justify-end text-sm font-semibold">
            <div class="flex items-center">
              <div class="text-gray-600 cursor-pointer p-2" @click="onCancel">
                {{ $t('Cancel') }}
              </div>
            </div>
            <div class="text-primary-700 cursor-pointer p-2" @click="onApply">
              {{ $t('Apply') }}
            </div>
          </div>
        </div>
      </Vueform>
    </div>
    <!-- Footer -->
    <!-- <div>
      <hr>
      <div class="flex items-center justify-end text-sm font-semibold">
        <div class="flex items-center">
          <div class="text-gray-600 cursor-pointer p-2" @click="onCancel">
            {{ $t('Cancel') }}
          </div>
        </div>
        <div class="text-primary-700 cursor-pointer p-2" @click="onApply">
          {{ $t('Apply') }}
        </div>
      </div>
    </div> -->
  </div>
</template>
