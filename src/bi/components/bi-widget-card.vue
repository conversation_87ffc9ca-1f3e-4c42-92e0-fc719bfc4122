<script setup>
import { generateChartConfig } from '@sensehawk/chart-generator';
import * as echarts from 'echarts';
import { onMounted, onUnmounted, reactive } from 'vue';

const props = defineProps({
  widget: {
    type: Object,
    required: true,
  },
});

const data_one = [
  {
    department: 'Engineering',
    budget: 500000,
  },
  {
    department: 'Engineering',
    budget: 200000,
  },
  {
    department: 'Marketing',
    budget: 300000,
  },
  {
    department: 'Marketing',
    budget: 150000,
  },
  {
    department: 'Sales',
    budget: 250000,
  },
  {
    department: 'HR',
    budget: 180000,
  },
  {
    department: 'HR',
    budget: 70000,
  },
  {
    department: 'Operations',
    budget: 220000,
  },
];

const config_one = {
  type: 'pie',
  data: {
    category: 'department',
    values: [
      'budget',
    ],
  },
  layout: {
    title: 'Department Budget Distribution',
    subtitle: 'subtitle goes here',
  },
  dataValues: {
    show: true,
    prefix: '$',
    compact: true,
    precision: 1,
  },
  legend: {
    show: true,
    position: 'right',
  },
};

const data_two = [
  {
    Category: 'Design',
    Quarter: 'Q1',
    Planned: '10',
    Actual: 8,
  },
  {
    Category: 'Design',
    Quarter: 'Q1',
    Planned: '10',
    Actual: 10,
  },
  {
    Category: 'Design',
    Quarter: 'Q2',
    Planned: '25',
    Actual: 22,
  },
  {
    Category: 'Engineering',
    Quarter: 'Q1',
    Planned: '15',
    Actual: 14,
  },
  {
    Category: 'Engineering',
    Quarter: 'Q2',
    Planned: '18',
    Actual: 16,
  },
  {
    Category: 'Design and Engineering',
    Quarter: 'Q2',
    Planned: '18',
    Actual: 30,
  },
];

const config_two = {
  type: 'horizontalBar',
  data: {
    category: 'Quarter',
    values: [
      'Planned',
    ],
    stackBy: null,
  },
  series: {
    Planned: {
      name: 'Planned',
      type: 'bar',
      yAxisIndex: 0,
      color: '#101828',
      lineColor: '#101828',
      lineWidth: 1,
      lineStyle: 'solid',
      smooth: false,
      prefix: '',
      suffix: '',
    },
  },
  layout: {
    title: null,
    subtitle: null,
  },
  legend: {
    show: true,
    position: 'bottom',
  },
  dataValues: {
    show: false,
    compact: false,
    precision: null,
  },
  styling: {
    colors: [
      '#77BEF0',
      '#FFCB61',
      '#FF894F',
      '#EA5B6F',
      '#9B59B6',
      '#2ECC71',
      '#F39C12',
    ],
  },
  axes: {
    categoryName: null,
    valueName: null,
    categoryLabels: 'show',
    valueLabels: 'show',
    secondaryValueLabels: 'show',
    valueScale: 'linear',
    secondaryValueScale: 'linear',
  },
  accessibility: {
    enabled: false,
    decalPatterns: false,
  },
  interactions: {
    dataZoom: {
      enabled: false,
      type: 'disabled',
    },
  },
};

const state = reactive({
  resize_observer: null,
});

let chart_instance = null;

onMounted(async () => {
  if (!props.widget.widget_id)
    return;
  let echarts_config;
  if (props.widget.widget_id === 'first_widget')
    echarts_config = generateChartConfig(data_one, config_one);
  else if (props.widget.widget_id === 'second_widget')
    echarts_config = generateChartConfig(data_two, config_two);

  const chartElement = document.getElementById(props.widget.widget_id);
  chart_instance = echarts.init(chartElement);

  chart_instance.setOption(echarts_config);

  state.resize_observer = new ResizeObserver(() => {
    chart_instance?.resize();
  });

  state.resize_observer.observe(chartElement);
});

onUnmounted(() => {
  const chartElement = document.getElementById(props.widget.widget_id);
  if (chartElement) {
    state.resize_observer?.unobserve(chartElement);
  }
  chart_instance?.dispose();
});
</script>

<template>
  <div class="w-full h-full">
    <div :id="props.widget.widget_id" class="w-full h-full" />
  </div>
</template>
