<script setup>
import { isEqual } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted } from 'vue';
import { BI_CHART_BUILDER_TABS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['go-back']);

const bi_store = useBiStore();
const { chart_builder_config, are_chart_builder_fields_filled } = storeToRefs(bi_store);

const chart_type_form$ = ref(null);
const form$ = ref(null);

const chart_types = [
  ['table', 'Table', IconHawkTableTwo],
  ['column_chart', 'Column chart', IconHawkHorizontalBarChartOne],
  ['horizontalBar_chart', 'Bar chart', IconHawkHorizontalBarChartOne],
  ['line_chart', 'Line chart', IconHawkLineChartUpOne],
  ['area_chart', 'Area chart', IconHawkAreaChart],
  ['mixed_chart', 'Mixed chart', IconHawkMixChart],
  ['pie_chart', 'Pie chart', IconHawkPieChartThree],
  ['donut_chart', 'Doughnut chart', IconHawkDoughnutChart],
  ['scatter_chart', 'Scatter chart', IconHawkScatterChart],
  ['gauge_chart', 'Gauge chart', IconHawkGaugeChart],
  ['progress_chart', 'Progress chart', IconHawkGaugeChart],
  ['heatmap_chart', 'Heatmap chart', IconHawkHeatmapChart],
  ['pyramid_chart', 'Pyramid chart', IconHawkPyramidChart],
  ['funnel_chart', 'Funnel chart', IconHawkFunnelChart],
  ['pareto_chart', 'Pareto chart', IconHawkParetoChart],
  ['waterfall_chart', 'Waterfall chart', IconHawkWaterfallChart],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
    icon: item[2],
  };
});

const state = reactive({
  form_data: {},
  active_item: 'layout',
});

const tabs = computed(() => {
  const current_chart_tabs = BI_CHART_BUILDER_TABS[chart_builder_config.value.chart_type] || [];
  return [
    ...(current_chart_tabs.includes('layout') ? [{ uid: 'layout', label: 'Layout' }] : []),
    ...(current_chart_tabs.includes('display') ? [{ uid: 'display', label: 'Display' }] : []),
    ...(current_chart_tabs.includes('axes') ? [{ uid: 'axes', label: 'Axes' }] : []),
    ...(current_chart_tabs.includes('advanced') ? [{ uid: 'advanced', label: 'Advanced' }] : []),
    ...(current_chart_tabs.includes('conditional_formatting') ? [{ uid: 'conditional_formatting', label: 'Conditional formatting' }] : []),
  ];
});

function onSeriesConfigChange(fields, index) {
  if (state.form_data?.layout_values?.[index]) {
    let has_chart_type_changed = false;
    Object.keys(fields).forEach((field_name) => {
      if (field_name === 'chart_type' && state.form_data.layout_values[index].chart_type && state.form_data.layout_values[index].chart_type !== fields.chart_type)
        has_chart_type_changed = true;
      state.form_data.layout_values[index][field_name] = fields[field_name];
    });
    if (has_chart_type_changed)
      chart_type_form$.value.update({ chart_type: 'mixed_chart' });
  }
}

function onReferenceLineConfigChange(fields, index) {
  if (state.form_data?.reference_lines?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data.reference_lines[index][field_name] = fields[field_name];
    });
  }
}

async function onChartTypeChange(new_chart_type, old_chart_type) {
  const chart_types_with_series = ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'];
  const is_chart_incompatible_with_the_previous = (chart_types_with_series.includes(old_chart_type) && !chart_types_with_series.includes(new_chart_type)) || (chart_types_with_series.includes(new_chart_type) && !chart_types_with_series.includes(old_chart_type));
  if (is_chart_incompatible_with_the_previous) {
    form$.value.reset();
    form$.value.update({
      layout_values: chart_types_with_series.includes(new_chart_type) ? [] : null,
    });
  }

  chart_builder_config.value = {
    ...chart_builder_config.value,
    chart_type: new_chart_type,
  };
  state.active_item = 'layout';

  if (!is_chart_incompatible_with_the_previous && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart'].includes(new_chart_type)) {
    await nextTick();
    form$.value.update({
      layout_values: state.form_data.layout_values.map((item) => {
        let chart_type = new_chart_type.replace('_chart', '');
        if (chart_type === 'column' || chart_type === 'horizontalBar')
          chart_type = 'bar';
        return { ...item, chart_type };
      }),
    });
  }
  if (new_chart_type === 'pareto_chart') {
    await nextTick();
    form$.value.update({
      show_eighty_percent_line: false,
      eighty_percent_line_color: '#F97066',
      eighty_percent_line_style: 'solid',
      cumulative_line_color: '#FDB022',
      bar_color: '#32D583',
    });
  }
  else if (new_chart_type === 'waterfall_chart') {
    await nextTick();
    form$.value.update({
      show_sum: false,
      positive_color: '#32D583',
      negative_color: '#F97066',
      sum_color: '#2E90FA',
    });
  }
  await validateForm();
}

async function validateForm() {
  setTimeout(async () => {
    await form$.value?.validate?.();
    if (form$.value?.invalid === false)
      are_chart_builder_fields_filled.value = true;
    else
      are_chart_builder_fields_filled.value = false;
  }, 0);
}

watch(() => state.form_data, async (new_config, old_config) => {
  await validateForm();
  chart_builder_config.value = {
    ...chart_builder_config.value,
    ...new_config,
  };
  if (!isEqual(new_config.stack_by, old_config.stack_by) && new_config.stack_by) {
    form$.value.update({
      layout_values: state.form_data.layout_values.map(item => ({ ...item, stack: true })),
    });
  }
}, { deep: true });

onMounted(() => {
  chart_builder_config.value = {
    chart_type: 'table',
  };
  // TODO: The below lines should be executed when you are opening the builder for an existing widget
  // chart_builder_config.value = {
  //   ...chart_builder_config.value,
  //   ...state.form_data,
  // };
});
</script>

<template>
  <div>
    <div
      class="flex items-center gap-2 cursor-pointer hover:underline text-sm font-semibold text-gray-700"
      @click="emit('go-back')"
    >
      <IconHawkArrowLeft />
      Back to data builder
    </div>

    <Vueform
      ref="chart_type_form$"
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      class="mt-6"
    >
      <SelectElement
        name="chart_type"
        default="table"
        :label="$t('Chart type')"
        :items="chart_types"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        @change="onChartTypeChange"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <component :is="option.icon" class="text-gray-500" :class="{ '-rotate-90': option.value === 'column_chart' }" />
            {{ option.label }}
          </div>
        </template>
        <template #single-label="{ value }">
          <div class="w-full flex items-center gap-2 px-2">
            <component :is="value.icon" class="text-gray-500" :class="{ '-rotate-90': value.value === 'column_chart' }" />
            {{ value.label }}
          </div>
        </template>
      </SelectElement>
    </Vueform>
    <HawkTabs v-if="chart_builder_config.chart_type" :tabs="tabs" :active_item="state.active_item" class="col-span-12 mt-6" @tab-click="state.active_item = $event.uid" />
    <Vueform
      :key="chart_builder_config.chart_type"
      ref="form$"
      v-model="state.form_data"
      sync
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      :display-errors="false"
      :display-messages="false"
      :messages="{ required: $t('This field is required') }"
      class="mt-3"
    >
      <div v-show="state.active_item === 'layout'" :key="chart_builder_config.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartLayoutTab
          :chart-type="chart_builder_config.chart_type"
          :chart-config="chart_builder_config"
          :form-instance="form$"
          @series-config-change="onSeriesConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'display'" :key="chart_builder_config.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartDisplayTab
          :chart-type="chart_builder_config.chart_type"
          :chart-config="chart_builder_config"
          :form-instance="form$"
        />
      </div>
      <div v-show="state.active_item === 'axes'" :key="chart_builder_config.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAxesTab
          :chart-type="chart_builder_config.chart_type"
          :chart-config="chart_builder_config"
        />
      </div>
      <div v-show="state.active_item === 'advanced'" :key="chart_builder_config.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAdvancedTab
          :chart-type="chart_builder_config.chart_type"
          :chart-config="chart_builder_config"
          :form-instance="form$"
          @reference-line-config-change="onReferenceLineConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'conditional_formatting'" :key="chart_builder_config.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartConditionalFormattingTab />
      </div>
    </Vueform>
  </div>
</template>
