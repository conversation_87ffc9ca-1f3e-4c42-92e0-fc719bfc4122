<script setup>
import { BI_CHART_COLOR_PALETTES, BI_HEATMAP_PALETTES } from '~/bi/constants/bi-constants';

const props = defineProps({
  variant: {
    type: String,
    default: 'default',
    validator(value) {
      return ['default', 'heatmap'].includes(value);
    },
  },
  name: {
    type: String,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<template>
  <SelectElement
    :name="props.name"
    :items="variant === 'default' ? BI_CHART_COLOR_PALETTES : BI_HEATMAP_PALETTES"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
    default="palette0"
    v-bind="props.options"
  >
    <template #option="{ option }">
      <div class="flex items-center gap-2">
        <div
          class="flex gap-1 p-1"
          :class="{
            'rounded bg-gray-900': option.label.dark,
            '!gap-0': variant === 'heatmap',
          }"
        >
          <div
            v-for="color in option.label.colors"
            :key="color"
            class="w-3 h-3 rounded-full"
            :class="{
              'rounded-none': variant === 'heatmap',
            }"
            :style="{ backgroundColor: color }"
          />
        </div>
      </div>
    </template>
    <template #single-label="{ value }">
      <div class="w-full flex items-center gap-2 px-2">
        <div
          class="flex gap-1 p-1"
          :class="{
            'rounded bg-gray-900': value.label.dark,
            '!gap-0': variant === 'heatmap',
          }"
        >
          <div
            v-for="color in value.label.colors"
            :key="color"
            class="w-3 h-3 rounded-full"
            :class="{
              'rounded-none': variant === 'heatmap',
            }"
            :style="{ backgroundColor: color }"
          />
        </div>
      </div>
    </template>
  </SelectElement>
</template>
