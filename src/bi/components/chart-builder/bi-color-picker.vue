<script setup>
import { BI_SERIES_COLORS } from '~/bi/constants/bi-constants';

const props = defineProps({
  activeColor: {
    type: String,
    default: null,
  },
  type: {
    type: String,
    default: 'plain',
    validator(value) {
      return ['plain', 'outlined'].includes(value);
    },
  },
});

const emit = defineEmits(['colorSelected']);
</script>

<template>
  <HawkMenu position="fixed" additional_trigger_classes="p-0 m-0 !ring-0 !border-0 focus:!ring-0">
    <template #trigger="{ open }">
      <div v-if="props.type === 'plain'" class="w-3 h-3 rounded-full mt-[5px]" :style="{ backgroundColor: props.activeColor }" />
      <div v-else class="border rounded p-1.5 flex items-center gap-1">
        <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: props.activeColor }" />
        <IconHawkChevronUp v-if="open" class="w-4 h-4" />
        <IconHawkChevronDown v-else class="w-4 h-4" />
      </div>
    </template>
    <template #content>
      <div class="flex flex-wrap gap-x-3 gap-y-2 w-[355px] p-3">
        <div
          v-for="color in BI_SERIES_COLORS"
          :key="color"
          class="p-0.5 rounded-full border border-transparent cursor-pointer"
          :class="{ '!border-gray-300': props.activeColor === color }"
          @click="emit('colorSelected', color)"
        >
          <div
            class="w-4 h-4 rounded-full"
            :style="{ backgroundColor: color }"
          />
        </div>
      </div>
    </template>
  </HawkMenu>
</template>
