<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const show_legend_position = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'pie_chart', 'donut_chart', 'scatter_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType));

const show_values = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'pie_chart', 'donut_chart', 'scatter_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType));

const show_radius = computed(() => ['pie_chart', 'donut_chart'].includes(props.chartType));

const show_symbol_size = computed(() => ['scatter_chart'].includes(props.chartType));

const show_cell_labels = computed(() => ['heatmap_chart'].includes(props.chartType));

const show_labels = computed(() => ['pyramid_chart', 'funnel_chart'].includes(props.chartType));

const show_conversion_rates = computed(() => ['funnel_chart'].includes(props.chartType));

const show_color_palette = computed(() => ['pie_chart', 'donut_chart'].includes(props.chartType));

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const state = reactive({
  eighty_percent_line_color: null,
  eighty_percent_line_style: null,
  cumulative_line_color: null,
  bar_color: null,
  positive_color: null,
  negative_color: null,
  sum_color: null,
});

function onFieldSelected(field, value) {
  state[field] = value;
  props.formInstance.update({
    [field]: value,
  });
}

watch(() => props.chartConfig, () => {
  ['eighty_percent_line_color', 'eighty_percent_line_style', 'cumulative_line_color', 'bar_color', 'positive_color', 'negative_color', 'sum_color'].forEach((key) => {
    state[key] = props.chartConfig[key];
  });
});
</script>

<template>
  <TextElement
    name="title"
    :label="$t('Title')"
    :placeholder="$t('Enter title')"
    description="Main chart title displayed at the top"
  />
  <TextElement
    name="subtitle"
    :label="$t('Subtitle')"
    :placeholder="$t('Enter subtitle')"
    description="Secondary title below the main title"
  />
  <SelectElement
    v-if="show_legend_position"
    name="legend_position"
    label="Legend position"
    :items="{
      none: $t('None'),
      top: $t('Top'),
      bottom: $t('Bottom'),
      left: $t('Left'),
      right: $t('Right'),
    }"
    default="bottom"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <SelectElement
    v-if="show_values"
    name="values"
    :label="$t('Values')"
    :items="{
      show: $t('Show'),
      hide: $t('Hide'),
    }"
    default="hide"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <TextElement
    input-type="number"
    name="precision"
    label="Precision"
    :conditions="[['values', 'show']]"
    placeholder="Enter precision"
  />
  <ToggleElement
    name="compact"
    label="Compact"
    :conditions="[['values', 'show']]"
  />
  <SliderElement
    v-if="show_radius"
    name="inner_radius"
    label="Inner radius"
    :min="20"
    :max="80"
    default="40"
    :step="1"
  />
  <SliderElement
    v-if="show_radius"
    name="outer_radius"
    label="Outer radius"
    :min="80"
    :max="100"
    default="100"
    :step="1"
  />
  <SliderElement
    v-if="show_symbol_size"
    name="symbol_size"
    label="Symbol size"
    :min="5"
    :max="50"
    default="10"
    :step="1"
  />
  <ToggleElement
    v-if="show_cell_labels"
    name="cell_labels"
    label="Cell labels"
  />
  <ToggleElement
    v-if="show_labels"
    name="labels"
    label="Labels"
  />
  <ToggleElement
    v-if="show_conversion_rates"
    name="conversion_rates"
    label="Conversion rates"
  />
  <template v-if="show_color_palette">
    <BiColorPalettePicker name="color_palette" :options="{ label: 'Color palette' }" />
  </template>
  <template v-if="['pareto_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Show 80% line
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_eighty_percent_line"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Line color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.eighty_percent_line_color"
          @color-selected="onFieldSelected('eighty_percent_line_color', $event)"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Line style
      </div>
      <div class="col-span-8">
        <HawkButtonGroup
          :items="line_styles"
          icon
          size="sm"
          :active_item="state.eighty_percent_line_style"
          class="w-fit"
          @select="onFieldSelected('eighty_percent_line_style', $event.uid)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Cumulative line color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.cumulative_line_color"
          @color-selected="onFieldSelected('cumulative_line_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Bar color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.bar_color"
          @color-selected="onFieldSelected('bar_color', $event)"
        />
      </div>
    </div>
    <HiddenElement name="eighty_percent_line_color" />
    <HiddenElement name="eighty_percent_line_style" />
    <HiddenElement name="cumulative_line_color" />
    <HiddenElement name="bar_color" />
  </template>
  <template v-if="['heatmap_chart'].includes(props.chartType)">
    <SelectElement
      name="color_type"
      label="Color type"
      :items="{
        piecewise: 'Piecewise',
        continuous: 'Continuous',
      }"
      default="continuous"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <BiColorPalettePicker
      name="color_scheme"
      :variant="props.chartConfig.color_type === 'continuous' ? 'heatmap' : 'default'"
      :options="{ label: 'Color scheme' }"
    />
    <ToggleElement
      name="color_scale"
      label="Color scale"
    />
    <SelectElement
      name="color_scale_position"
      label="Color scale position"
      :items="{
        top: 'Top',
        bottom: 'Bottom',
        left: 'Left',
        right: 'Right',
      }"
      :conditions="[['color_scale', '==', true]]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
      default="right"
    />
  </template>
  <template v-if="['waterfall_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Show sum
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_sum"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Positive color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.positive_color"
          @color-selected="onFieldSelected('positive_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Negative color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.negative_color"
          @color-selected="onFieldSelected('negative_color', $event)"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_sum" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Sum color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.sum_color"
          @color-selected="onFieldSelected('sum_color', $event)"
        />
      </div>
    </div>
    <HiddenElement name="positive_color" />
    <HiddenElement name="negative_color" />
    <HiddenElement name="sum_color" />
  </template>
</template>
