<script setup>
import { storeToRefs } from 'pinia';
import { BI_SERIES_COLORS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['referenceLineConfigChange']);

const bi_store = useBiStore();
const { chart_builder_data_types } = storeToRefs(bi_store);

function onReferenceLineItemChange(event, index) {
  emit('referenceLineConfigChange', event, index);
}

async function onAddReferenceLine(index) {
  await nextTick();
  emit('referenceLineConfigChange', {
    color: BI_SERIES_COLORS[Math.floor(Math.random() * 30)],
    label: `Reference line ${index + 1}`,
    value: null,
    line_style: 'solid',
  }, index);
}
</script>

<template>
  <template v-if="['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType)">
    <SelectElement
      v-if="props.chartConfig.layout_category && chart_builder_data_types[props.chartConfig.layout_category] === 'date' && props.chartType !== 'horizontalBar_chart'"
      name="data_zoom"
      label="Data zoom"
      :items="{
        disabled: 'Disabled',
        slider: 'Slider',
        inside: 'Inside',
        both: 'Both',
      }"
      default="disabled"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ListElement
      name="reference_lines"
      label="Reference lines"
      add-text="+ Add reference line"
      :controls="{ add: true, remove: false, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddReferenceLine"
    >
      <template #default="{ index }">
        <ObjectElement :name="index">
          <TextElement
            name="label"
            @change="onReferenceLineItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.reference_lines?.[index]?.color"
                @color-selected="onReferenceLineItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <BiReferenceLineContextMenu
                  :reference-line-config="props.chartConfig?.reference_lines?.[index]"
                  @field-selected="onReferenceLineItemChange($event, index)"
                />
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.reference_lines.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <HiddenElement name="color" />
          <HiddenElement name="value" />
          <HiddenElement name="line_style" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
</template>
