<script setup>
const props = defineProps({
  referenceLineConfig: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected']);

const state = reactive({
  is_menu_open: false,
  color: null,
  label: null,
  value: null,
  line_style: null,
});

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

function onFieldSelected(field_name, value) {
  state[field_name] = value;
  emit('fieldSelected', { [field_name]: value });
}

function onMenuOpen() {
  Object.keys(props.referenceLineConfig).forEach((key) => {
    state[key] = props.referenceLineConfig[key];
  });
  state.is_menu_open = true;
}
</script>

<template>
  <div class="flex items-center gap-1">
    <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0" @open="onMenuOpen" @close="state.is_menu_open = false">
      <template #trigger>
        <IconHawkDotsVertical class="w-4 h-4" />
      </template>
      <template #content>
        <div class="px-3.5 py-3">
          <div class="mb-3">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">
              Configure reference line
            </h3>
            <hr class="-mx-3.5">
          </div>
          <Vueform
            v-if="state.is_menu_open"
            size="sm"
            :columns="{
              default: { container: 12, label: 4, wrapper: 12 },
              sm: { container: 12, label: 4, wrapper: 12 },
              md: { container: 12, label: 4, wrapper: 12 },
            }"
            :add-classes="{
              FormElements: {
                container: '!gap-y-4',
              },
            }"
          >
            <div class="col-span-12">
              <div class="flex items-center gap-3">
                <BiColorPicker :active-color="state.color" @color-selected="onFieldSelected('color', $event)" />
                <div class="flex-1">
                  <TextElement
                    name="label"
                    :default="state.label"
                    @change="onFieldSelected('label', $event)"
                  />
                </div>
              </div>
            </div>

            <TextElement
              name="value"
              label="Value"
              input-type="number"
              :default="state.value"
              placeholder="Enter value"
              :add-classes="{
                TextElement: {
                  inputContainer: '!h-7',
                },
              }"
              @change="onFieldSelected('value', $event)"
            />

            <div class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Line style</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="line_styles"
                    icon
                    size="sm"
                    :active_item="state.line_style"
                    class="w-fit"
                    @select="onFieldSelected('line_style', $event.uid)"
                  />
                </div>
              </div>
            </div>
          </Vueform>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
