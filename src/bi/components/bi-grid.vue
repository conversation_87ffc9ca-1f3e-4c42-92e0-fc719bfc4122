<script setup>
import { GridLayout } from 'grid-layout-plus';

const layout = reactive([
  { x: 0, y: 0, w: 10, h: 15, i: '0', widget_id: 'first_widget', static: false },
  { x: 0, y: 0, w: 8, h: 10, i: '1', widget_id: 'second_widget', static: false },
]);
</script>

<template>
  <GridLayout v-model:layout="layout" :row-height="30" class="-m-4">
    <template #item="{ item }">
      <BiWidgetCard :widget="item" />
    </template>
  </GridLayout>
</template>

<style scoped>
.vgl-layout {
  @apply bg-gray-200;
}

.vgl-layout::before {
  position: absolute;
  width: calc(100% - 5px);
  height: calc(100% - 5px);
  margin: 5px;
  content: '';
  background-image: linear-gradient(to right, lightgrey 1px, transparent 1px),
    linear-gradient(to bottom, lightgrey 1px, transparent 1px);
  background-repeat: repeat;
  background-size: calc(calc(100% - 5px) / 12) 40px;
}

:deep(.vgl-item:not(.vgl-item--placeholder)) {
  background-color: white;
  border: 1px solid black;
}

:deep(.vgl-item--resizing) {
  opacity: 90%;
}

:deep(.vgl-item--static) {
  background-color: #cce;
}

.text {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  margin: auto;
  font-size: 24px;
  text-align: center;
}
</style>
