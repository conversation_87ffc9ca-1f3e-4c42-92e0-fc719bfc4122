<script setup>
import { storeToRefs } from 'pinia';
import { reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);

const { copy } = useClipboard();
const bi_store = useBiStore();

const state = reactive({
  is_copied: false,
});

const { sql_query } = storeToRefs(bi_store);

async function copyToClipboard() {
  copy(sql_query.value);
  state.is_copied = true;
  setTimeout(() => {
    state.is_copied = false;
  }, 2000);
}
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1 max-h-[calc(100vh-85px)] overflow-auto">
      {{ bi_store.data }}
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton @click="emit('continue')">
        <IconHawkBarChartTen />
        Configure chart
        <IconHawkArrowRight />
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show SQL Query"
      hide-text="Hide SQL Query"
    >
      <template #default="{ height }">
        <div class="p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-1 text-sm font-semibold text-gray-700">
              <IconHawkDatabaseTwo />
              SQL Query
            </div>
            <div class="flex items-center gap-2">
              <button
                class="text-xs font-medium text-gray-700 flex items-center gap-1"
                @click="copyToClipboard"
              >
                <IconHawkCheckCircleGreen v-if="state.is_copied" class="w-4 h-4 text-green-600" />
                <IconHawkCopyOne v-else class="w-4 h-4" />
                {{ state.is_copied ? 'Copied!' : 'Copy' }}
              </button>
            </div>
          </div>
          <div class="overflow-auto scrollbar" :style="{ height: `${height - 80}px` }">
            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono whitespace-pre-wrap overflow-auto h-full">{{ sql_query }}</pre>
          </div>
        </div>
      </template>
    </BiBottomDrawer>
  </div>
</template>
