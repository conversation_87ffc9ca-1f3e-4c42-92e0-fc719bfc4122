export const BI_SERIES_COLORS = ['#E478FA', '#FD6F8E', '#F97066', '#FF6B47', '#FDB022', '#FFD700', '#F7DC6F', '#BDFF5C', '#66C61C', '#32D583', '#2ECC71', '#1ABC9C', '#2ED3B7', '#17A2B8', '#48CAE4', '#2E90FA', '#3B82F6', '#4F46E5', '#6366F1', '#8B5CF6', '#A48AFB', '#C084FC', '#D946EF', '#EC4899', '#F43F5E', '#EF4444', '#DC2626', '#B91C1C', '#7F1D1D', '#450A0A'];

export const BI_CHART_COLOR_PALETTES = {
  palette0: {
    colors: ['#77BEF0', '#FFCB61', '#FF894F', '#EA5B6F', '#9B59B6', '#2ECC71', '#F39C12'],
  },
  palette1: {
    colors: ['#FF595E', '#FFCA3A', '#8AC926', '#1982C4', '#6A4C93', '#FF8B3D', '#2EC4B6'],
  },
  palette2: {
    colors: ['#264653', '#2A9D8F', '#E9C46A', '#F4A261', '#E76F51', '#48846B', '#83372A'],
  },
  palette3: {
    colors: ['#f94144', '#f3722c', '#f8961e', '#f9c74f', '#90be6d', '#43aa8b', '#577590'],
  },
  palette4: {
    colors: ['#335C67', '#FFF3B0', '#E09F3E', '#9E2A2B', '#540B0E', '#7B4B3D', '#124559'],
  },
};

export const BI_HEATMAP_PALETTES = {
  // https://coolors.co/palette/03071e-370617-6a040f-9d0208-d00000-dc2f02-e85d04-f48c06-faa307-ffba08
  palette0: {
    colors: ['#FFBA08', '#FAA307', '#F48C06', '#E85D04', '#DC2F02', '#D00000', '#9D0208', '#6A040F', '#370617'],
  },
  palette1: {
    colors: ['#370617', '#6A040F', '#9D0208', '#D00000', '#DC2F02', '#E85D04', '#F48C06', '#FAA307', '#FFBA08'],
  },
  // https://coolors.co/palette/03045e-023e8a-0077b6-0096c7-00b4d8-48cae4-90e0ef-ade8f4-caf0f8
  palette2: {
    colors: ['#CAF0F8', '#ADE8F4', '#90E0EF', '#48CAE4', '#00B4D8', '#0096C7', '#0077B6', '#023E8A', '#03045E'],
  },
  palette3: {
    colors: ['#03045E', '#023E8A', '#0077B6', '#0096C7', '#00B4D8', '#48cae4', '#90e0ef', '#ade8f4', '#caf0f8'],
  },
  // https://coolors.co/palette/ff7b00-ff8800-ff9500-ffa200-ffaa00-ffb700-ffc300-ffd000-ffdd00-ffea00
  palette4: {
    colors: ['#FFEA00', '#FFDD00', '#FFD000', '#FFC300', '#FFB700', '#FFAA00', '#FFA200', '#FF9500', '#FF8800', '#FF7B00'],
  },
  palette5: {
    colors: ['#FF7B00', '#FF8800', '#FF9500', '#FFA200', '#FFAA00', '#FFB700', '#FFC300', '#FFD000', '#FFDD00', '#FFEA00'],
  },
};

export const BI_CHART_BUILDER_TABS = {
  table: ['layout', 'display', 'conditional_formatting'],
  column_chart: ['layout', 'display', 'axes', 'advanced'],
  horizontalBar_chart: ['layout', 'display', 'axes', 'advanced'],
  line_chart: ['layout', 'display', 'axes', 'advanced'],
  area_chart: ['layout', 'display', 'axes', 'advanced'],
  mixed_chart: ['layout', 'display', 'axes', 'advanced'],
  pie_chart: ['layout', 'display'],
  donut_chart: ['layout', 'display'],
  scatter_chart: ['layout', 'display', 'axes'],
  gauge_chart: ['layout', 'display', 'advanced'],
  progress_chart: ['layout', 'display', 'advanced'],
  heatmap_chart: ['layout', 'display'],
  pyramid_chart: ['layout', 'display'],
  funnel_chart: ['layout', 'display'],
  pareto_chart: ['layout', 'display'],
  waterfall_chart: ['layout', 'display', 'axes'],
};

export const CHART_TO_CATEGORY_TYPE_MAP = {
  column_chart: ['text', 'date', 'boolean'],
  horizontalBar_chart: ['text', 'date', 'boolean'],
  line_chart: ['text', 'date', 'boolean'],
  area_chart: ['text', 'date', 'boolean'],
  mixed_chart: ['text', 'date', 'boolean'],
  pie_chart: ['text'],
  donut_chart: ['text'],
  scatter_chart: ['numeric'],
  heatmap_chart: ['text'],
  pyramid_chart: ['text'],
  funnel_chart: ['text'],
  pareto_chart: ['text'],
  waterfall_chart: ['text'],
};

export const CHART_TO_VALUE_TYPE_MAP = {
  column_chart: ['numeric'],
  horizontalBar_chart: ['numeric'],
  line_chart: ['numeric'],
  area_chart: ['numeric'],
  mixed_chart: ['numeric'],
  pie_chart: ['numeric'],
  donut_chart: ['numeric'],
  scatter_chart: ['numeric'],
  heatmap_chart: ['numeric', 'text'],
  gauge_chart: ['numeric'],
  progress_chart: ['numeric'],
  pyramid_chart: ['numeric'],
  funnel_chart: ['numeric'],
  pareto_chart: ['numeric'],
  waterfall_chart: ['numeric'],
};

export const DUAL_Y_AXIS_SUPPORTED_CHARTS = ['column_chart', 'line_chart', 'area_chart', 'mixed_chart'];
