import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawkArrowUp from '~icons/hawk/arrow-up';
import IconHawkBinary from '~icons/hawk/binary';
import IconHawkBoolean from '~icons/hawk/boolean';
import IconHawkBracketsEllipses from '~icons/hawk/brackets-ellipses';
import calendar from '~icons/hawk/calendar';
import IconHawkClock from '~icons/hawk/clock';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkInnerJoin from '~icons/hawk/inner-join';
import IconHawkOuterJoin from '~icons/hawk/outer-join';
import IconHawkRightJoin from '~icons/hawk/right-join';
import IconHawkTypeOne from '~icons/hawk/type-one';
import IconHawkLeftJoin from '~icons/hawk/vector-join';

function getIconsForType(type) {
  const icons_type_map = {
    'string': IconHawkTypeOne,
    'text': IconHawkTypeOne,
    'varchar': IconHawkTypeOne,
    'char': IconHawkTypeOne,
    'numeric': IconHawkHashTwo,
    'integer': IconHawkHashTwo,
    'bigint': IconHawkHashTwo,
    'smallint': IconHawkHashTwo,
    'decimal': IconHawkHashTwo,
    'float': IconHawkHashTwo,
    'double': IconHawkHashTwo,
    'real': IconHawkHashTwo,
    'number': IconHawkHashTwo,
    'double precision': IconHawkHashTwo,
    'date': calendar,
    'datetime': calendar,
    'timestamp': calendar,
    'timestamptz': IconHawkClock,
    'boolean': IconHawkBoolean,
    'bool': IconHawkBoolean,
    'interval': IconHawkClock,
    'time': IconHawkClock,
    'binary': IconHawkBinary,
    'bytea': IconHawkBinary,
    'json': IconHawkBracketsEllipses,
    'jsonb': IconHawkBracketsEllipses,
    'uuid': IconHawkHashTwo,
    'id': IconHawkHashTwo,
    'function': IconHawkFunction,
    'formula': IconHawkFormula,
    'asc': IconHawkArrowUp,
    'desc': IconHawkArrowDown,
    'joins': {
      inner: IconHawkInnerJoin,
      outer: IconHawkOuterJoin,
      right: IconHawkRightJoin,
      left: IconHawkLeftJoin,
    },
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

function getOperatorsForType(type) {
  const operators_type_map = {
    text: [{ label: 'concat', output_type: 'text' }],
    numeric: [{ label: 'sum', output_type: 'numeric' }, { label: 'avg', output_type: 'numeric' }, { label: 'max', output_type: 'numeric' }, { label: 'min', output_type: 'numeric' }, { label: 'count', output_type: 'numeric' }],
    date: [{ label: 'min', output_type: 'date' }, { label: 'max', output_type: 'date' }],
  };

  const operators_for_type = {
    integer: operators_type_map.numeric,
    float: operators_type_map.numeric,
    date: operators_type_map.date,
    text: operators_type_map.text,
    numeric: operators_type_map.numeric,
    timestamp: operators_type_map.date,
  };

  return operators_for_type[type] || [];
}

export function useBIQueryBuilder() {
  const getColumnText = column_name => `${column_name}`;
  const getAggregationText = agg => `${agg} of `;
  const getTableText = table_name => `${table_name} -- `;
  const constructAlias = (fn, param) => param ? fn(param) : ''; // this will construct the alias for the column ${table_name} -- ${agg} ${column_name}
  const constructColumnAlias = ({ table_name, column_name, agg }) => constructAlias(getTableText, table_name) + constructAlias(getAggregationText, agg) + constructAlias(getColumnText, column_name);

  return {
    constructColumnAlias,
    getIconsForType,
    getOperatorsForType,
  };
}
