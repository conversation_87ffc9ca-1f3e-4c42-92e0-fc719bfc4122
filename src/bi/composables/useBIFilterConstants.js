import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { useCommonImports } from '~/common/composables/common-imports.composable';

export function useBIFilterConstants() {
  dayjs.extend(quarterOfYear);
  const { $t } = useCommonImports();

  const DATE_FILTER_OPTIONS = {
    between: [
      { value: 'custom_range', label: `${$t('Custom range')}` },
      { value: 'last_7_days', label: `${$t('Last 7 days')}` },
      { value: 'next_7_days', label: `${$t('Next 7 days')}` },
      { value: 'last_30_days', label: `${$t('Last 30 days')}` },
      { value: 'next_30_days', label: `${$t('Next 30 days')}` },
      { value: 'week_to_date', label: `${$t('Week to date')}` },
      { value: 'this_week', label: `${$t('This week')}` },
      { value: 'last_week', label: `${$t('Last week')}` },
      { value: 'month_to_date', label: `${$t('Month to date')}` },
      { value: 'this_month', label: `${$t('This month')}` },
      { value: 'last_month', label: `${$t('Last month')}` },
      { value: 'quarter_to_date', label: `${$t('Quarter to date')}` },
      { value: 'this_quarter', label: `${$t('This quarter')}` },
      { value: 'last_quarter', label: `${$t('Last quarter')}` },
      { value: 'year_to_date', label: `${$t('Year to date')}` },
      { value: 'this_year', label: `${$t('This year')}` },
      { value: 'last_year', label: `${$t('Last year')}` },
    ],
    isNotBetween: [
      { value: 'custom_range', label: `${$t('Custom range')}` },
      { value: 'last_7_days', label: `${$t('Last 7 days')}` },
      { value: 'next_7_days', label: `${$t('Next 7 days')}` },
      { value: 'last_30_days', label: `${$t('Last 30 days')}` },
      { value: 'next_30_days', label: `${$t('Next 30 days')}` },
      { value: 'week_to_date', label: `${$t('Week to date')}` },
      { value: 'this_week', label: `${$t('This week')}` },
      { value: 'last_week', label: `${$t('Last week')}` },
      { value: 'month_to_date', label: `${$t('Month to date')}` },
      { value: 'this_month', label: `${$t('This month')}` },
      { value: 'last_month', label: `${$t('Last month')}` },
      { value: 'quarter_to_date', label: `${$t('Quarter to date')}` },
      { value: 'this_quarter', label: `${$t('This quarter')}` },
      { value: 'last_quarter', label: `${$t('Last quarter')}` },
      { value: 'year_to_date', label: `${$t('Year to date')}` },
      { value: 'this_year', label: `${$t('This year')}` },
      { value: 'last_year', label: `${$t('Last year')}` },
    ],
    isEqualTo: [
      { value: 'exact_date', label: `${$t('Exact date')}` },
      { value: 'today', label: `${$t('Today')}` },
      { value: 'yesterday', label: `${$t('Yesterday')}` },
      { value: 'last_7_days', label: `${$t('Last 7 days')}` },
      { value: 'next_7_days', label: `${$t('Next 7 days')}` },
      { value: 'last_30_days', label: `${$t('Last 30 days')}` },
      { value: 'next_30_days', label: `${$t('Next 30 days')}` },
      { value: 'week_to_date', label: `${$t('Week to date')}` },
      { value: 'this_week', label: `${$t('This week')}` },
      { value: 'last_week', label: `${$t('Last week')}` },
      { value: 'month_to_date', label: `${$t('Month to date')}` },
      { value: 'this_month', label: `${$t('This month')}` },
      { value: 'last_month', label: `${$t('Last month')}` },
      { value: 'quarter_to_date', label: `${$t('Quarter to date')}` },
      { value: 'this_quarter', label: `${$t('This quarter')}` },
      { value: 'last_quarter', label: `${$t('Last quarter')}` },
      { value: 'year_to_date', label: `${$t('Year to date')}` },
      { value: 'this_year', label: `${$t('This year')}` },
      { value: 'last_year', label: `${$t('Last year')}` },
    ],
    isNotEqualTo: [
      { value: 'exact_date', label: `${$t('Exact date')}` },
      { value: 'today', label: `${$t('Today')}` },
      { value: 'yesterday', label: `${$t('Yesterday')}` },
      { value: 'last_7_days', label: `${$t('Last 7 days')}` },
      { value: 'next_7_days', label: `${$t('Next 7 days')}` },
      { value: 'last_30_days', label: `${$t('Last 30 days')}` },
      { value: 'next_30_days', label: `${$t('Next 30 days')}` },
      { value: 'week_to_date', label: `${$t('Week to date')}` },
      { value: 'this_week', label: `${$t('This week')}` },
      { value: 'last_week', label: `${$t('Last week')}` },
      { value: 'month_to_date', label: `${$t('Month to date')}` },
      { value: 'this_month', label: `${$t('This month')}` },
      { value: 'last_month', label: `${$t('Last month')}` },
      { value: 'quarter_to_date', label: `${$t('Quarter to date')}` },
      { value: 'this_quarter', label: `${$t('This quarter')}` },
      { value: 'last_quarter', label: `${$t('Last quarter')}` },
      { value: 'year_to_date', label: `${$t('Year to date')}` },
      { value: 'this_year', label: `${$t('This year')}` },
      { value: 'last_year', label: `${$t('Last year')}` },
    ],
    isAfter: [
      { value: 'exact_date', label: `${$t('Exact date')}` },
      { value: 'today', label: `${$t('Today')}` },
      { value: 'after_start_of_this_week', label: `${$t('Start of this week')}` },
      { value: 'after_start_of_this_month', label: `${$t('Start of this month')}` },
      { value: 'after_start_of_this_quarter', label: `${$t('Start of this quarter')}` },
      { value: 'after_start_of_this_year', label: `${$t('Start of this year')}` },
    ],
    isBefore: [
      { value: 'exact_date', label: `${$t('Exact date')}` },
      { value: 'today', label: `${$t('Today')}` },
      { value: 'before_start_of_this_week', label: `${$t('Start of this week')}` },
      { value: 'before_start_of_this_month', label: `${$t('Start of this month')}` },
      { value: 'before_start_of_this_quarter', label: `${$t('Start of this quarter')}` },
      { value: 'before_start_of_this_year', label: `${$t('Start of this year')}` },
    ],
  };

  // Structure will be changed(WIP), this is just for testing filter UI
  const DUMMY_COLUMNS = [
    {
      label: 'ID',
      type: 'text',
      options: ['1', '2', '3', '4', '5'],
      default_operator: {
        label: 'Between',
        value: 'between',
        component: 'between',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'input',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'input',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'between',
        },
      ],
    },
    {
      label: 'Activity',
      type: 'text',
      options: ['Install framework', 'Install site furnishings', 'Place and finish site Paving'],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'checkbox',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
      ],
    },
    {
      label: 'Subactivity',
      type: 'text',
      options: ['Foundation work', 'Electrical setup', 'Plumbing installation', 'Interior finishing', 'Quality inspection'],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'checkbox',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
        {
          label: 'Contains',
          value: 'contains',
          component: 'input',
        },
      ],
    },
    {
      label: 'Layer',
      type: 'text',
      options: ['Base layer', 'Mid layer', 'Top layer', 'Surface layer'],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'checkbox',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
      ],
    },
    {
      label: 'Sublayer',
      type: 'text',
      options: ['A', 'B', 'C', 'D', 'E'],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'checkbox',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
      ],
    },
    {
      label: 'Date',
      type: 'date',
      default_operator: {
        label: 'Between',
        value: 'between',
        component: 'date_range',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'date_picker',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'date_picker',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'date_picker',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'date_range',
        },
      ],
    },
    {
      label: 'Scope',
      type: 'integer',
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'input',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'input',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'input',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'input',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'input',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'slider',
        },
      ],
    },
    {
      label: 'Work Done',
      type: 'integer',
      default_operator: {
        label: 'Greater than',
        value: 'greater_than',
        component: 'input',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'input',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'input',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'input',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'input',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'between',
        },
      ],
    },
    {
      label: 'Temperature',
      type: 'float',
      default_operator: {
        label: 'Between',
        value: 'between',
        component: 'between',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'input',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'input',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'input',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'between',
        },
      ],
    },
    {
      label: 'Status',
      type: 'text',
      options: ['Pending', 'In Progress', 'Completed', 'On Hold', 'Cancelled'],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'checkbox',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'checkbox',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'checkbox',
        },
      ],
    },
    {
      label: 'Created At',
      type: 'timestamp',
      default_operator: {
        label: 'Between',
        value: 'between',
        component: 'datetime_range',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'datetime_picker',
        },
        {
          label: 'Greater than',
          value: 'greater_than',
          component: 'datetime_picker',
        },
        {
          label: 'Less than',
          value: 'less_than',
          component: 'datetime_picker',
        },
        {
          label: 'Between',
          value: 'between',
          component: 'datetime_range',
        },
      ],
    },
    {
      label: 'Is Active',
      type: 'boolean',
      options: [true, false],
      default_operator: {
        label: 'Equal to',
        value: 'equal',
        component: 'radio',
      },
      operators: [
        {
          label: 'Equal to',
          value: 'equal',
          component: 'radio',
        },
        {
          label: 'Not equal to',
          value: 'not_equal',
          component: 'radio',
        },
      ],
    },
  ];

  function getFilterText(column_config, filter_config) {
    console.log('getFilterText::', column_config, filter_config);
    const column_label = column_config.agg ? `${column_config.agg} ${$t('of')} ${column_config.label}` : column_config.label;
    if (!filter_config) {
      return column_label;
    }

    if (filter_config.operator.value === 'between' && filter_config.operator.component === 'between') {
      return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value_min} and ${filter_config.operator_value_max}`;
    }
    if (filter_config.operator.value === 'between' && filter_config.operator.component === 'slider') {
      return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value[0]} and ${filter_config.operator_value[1]}`;
    }
    else if (['equal', 'not_equal', 'greater_than', 'less_than'].includes(filter_config.operator.value)) {
      return `${column_label} is ${filter_config.operator.label} ${Array.isArray(filter_config.operator_value) ? `${filter_config.operator_value.length} values` : filter_config.operator_value}`;
    }
    else {
      return column_label;
    }
  }

  return {
    // DEFAULT_COLUMNS,
    // DEFAULT_DISPLAY_FILTERS,
    DUMMY_COLUMNS,
    DATE_FILTER_OPTIONS,
    getFilterText,
  };
}
